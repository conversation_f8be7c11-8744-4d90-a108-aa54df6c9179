"""
Evaluation script for Test Plan Generation Agent
"""
#------------------------------------------------------------------------------
# IMPORTS
#------------------------------------------------------------------------------
# Standard library imports
import os
import sys
import json
from typing import Optional, List, Dict, Any, Union
import traceback
from datetime import datetime

# Third-party imports
import pandas as pd
import requests
import braintrust
from braintrust import Eval
from autoevals import Score
from autoevals.llm import OpenAILLMClassifier, build_classification_tools
from openai import OpenAI

# Add parent directory to path for local imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))

#------------------------------------------------------------------------------
# CONFIGURATION
#------------------------------------------------------------------------------
# Project settings
PROJECT_NAME = "Pedro repro 4015"
MODEL_NAME = "o3"

# File paths
PROMPT_PATH = os.path.join(os.path.dirname(__file__), 'prompts', 'test_plan_evaluator.md')
DATA_PATH = os.path.join(os.path.dirname(__file__), 'data', 'testplan_eval_dataset_big_other_notEmpty.csv')

# API endpoints
BRAINTRUST_API_BASE = "https://api.braintrust.dev/v1"
EXPERIMENT_LIST_URL = f"{BRAINTRUST_API_BASE}/experiment"

# OpenAI client for Braintrust with cache disabled
braintrust_client = OpenAI(
    base_url="https://api.braintrust.dev/v1/proxy",
    api_key=os.getenv("BRAINTRUST_API_KEY"),
    default_headers={
        "x-bt-use-cache": "never"
    }
)

# Grading scale for LLM classifier
GRADE_SCORES = {
    "A": 0.9,  # 90-100%
    "B": 0.8,  # 80-89% 
    "C": 0.7,  # 70-79%
    "D": 0.6,  # 60-69%
    "F": 0.5   # <60%
}

# Maximum tokens for LLM evaluation
MAX_TOKENS = 16384

# Temperature for LLM evaluation (0 = most deterministic)
TEMPERATURE = 0.0

# Default if extraction fails
DEFAULT_TEST_SUITE_NAME = "Unknown Test Suite"

#------------------------------------------------------------------------------
# UTILITY FUNCTIONS
#------------------------------------------------------------------------------
def load_prompt_from_file(file_path: str) -> str:
    """
    Load a prompt template from a file.
    
    Args:
        file_path: Path to the prompt template file
        
    Returns:
        The content of the prompt file as a string
        
    Raises:
        FileNotFoundError: If the prompt file doesn't exist
        IOError: If there's an error reading the file
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Error: Prompt file not found at {file_path}")
        raise
    except IOError as e:
        print(f"Error reading prompt file: {e}")
        raise

def extract_test_suite_name(test_plan: Union[str, Dict, List]) -> tuple[str, bool]:
    """
    Extract the test suite name from a test plan object or JSON string.
    
    Args:
        test_plan: The test plan data, either as a string, dict, or list
        
    Returns:
        A tuple containing:
        - The extracted test suite name or a default value
        - A boolean indicating whether extraction was successful
    """
    test_suite_name = DEFAULT_TEST_SUITE_NAME
    success = False
    
    try:
        # Try to parse the test plan as JSON if it's a string
        if isinstance(test_plan, str):
            try:
                test_plan_json = json.loads(test_plan)
            except json.JSONDecodeError:
                test_plan_json = test_plan
        else:
            test_plan_json = test_plan
        
        # Check if it's a list with a testSuite object
        if isinstance(test_plan_json, list) and len(test_plan_json) > 0:
            first_item = test_plan_json[0]
            if isinstance(first_item, dict) and 'testSuite' in first_item:
                if isinstance(first_item['testSuite'], dict) and 'name' in first_item['testSuite']:
                    test_suite_name = first_item['testSuite']['name']
                    success = True
        
        # If not found in the expected structure, look for any 'name' field
        elif isinstance(test_plan_json, dict):
            if 'testSuite' in test_plan_json and isinstance(test_plan_json['testSuite'], dict):
                if 'name' in test_plan_json['testSuite']:
                    test_suite_name = test_plan_json['testSuite']['name']
                    success = True
            elif 'name' in test_plan_json:
                test_suite_name = test_plan_json['name']
                success = True
    except Exception:
        # Silently handle exceptions and return the default
        pass
        
    return test_suite_name, success

def extract_row_index(event: Dict[str, Any]) -> Optional[int]:
    """
    Extract the row_index from an event object, checking multiple possible locations.
    
    Args:
        event: The event object from Braintrust API
        
    Returns:
        The row_index if found, or None otherwise
    """
    row_index = None
    
    # Check if there's a nested input structure
    nested_input = None
    if 'input' in event and isinstance(event['input'], dict) and 'input' in event['input']:
        nested_input = event['input']['input']
    
    # First check in event['input']['metadata']
    if 'input' in event and isinstance(event['input'], dict) and 'metadata' in event['input']:
        if isinstance(event['input']['metadata'], dict) and 'row_index' in event['input']['metadata']:
            row_index = event['input']['metadata']['row_index']
    
    # If not found, check in nested input
    if row_index is None and isinstance(nested_input, dict) and 'row_index' in nested_input:
        row_index = nested_input['row_index']
    
    return row_index

def extract_test_plan(event: Dict[str, Any]) -> Optional[Union[str, Dict, List]]:
    """
    Extract the test_plan from an event object, checking multiple possible locations.
    
    Args:
        event: The event object from Braintrust API
        
    Returns:
        The test_plan if found, or None otherwise
    """
    test_plan = None
    
    # Check if there's a nested input structure
    nested_input = None
    if 'input' in event and isinstance(event['input'], dict) and 'input' in event['input']:
        nested_input = event['input']['input']
    
    # Check in nested input for test_plan
    if nested_input and isinstance(nested_input, dict) and 'test_plan' in nested_input:
        test_plan = nested_input['test_plan']
    elif 'input' in event and isinstance(event['input'], dict) and 'test_plan' in event['input']:
        test_plan = event['input']['test_plan']
    
    return test_plan

#------------------------------------------------------------------------------
# DATA LOADING
#------------------------------------------------------------------------------
class DataLoader:
    """
    Loads evaluation data from CSV or XLSX files with robust error handling.
    
    Attributes:
        file_path: Path to the data file
        encoding: File encoding to use when reading CSV files
        file_extension: The extension of the data file
    """
    
    def __init__(self, file_path: str, encoding: str = 'utf-8'):
        """
        Initialize the DataLoader with a file path.
        
        Args:
            file_path: Path to the data file (CSV or XLSX)
            encoding: Character encoding for CSV files (default: utf-8)
        """
        self.file_path = file_path
        self.encoding = encoding
        self.file_extension = os.path.splitext(file_path)[1].lower()
    
    def _read_file(self) -> pd.DataFrame:
        """
        Read input file based on its extension.
        
        Returns:
            DataFrame containing the loaded data
            
        Raises:
            ValueError: If file extension is not supported
            Exception: For other file reading errors
        """
        try:
            if self.file_extension == '.csv':
                return pd.read_csv(self.file_path, encoding=self.encoding)
            elif self.file_extension in ['.xlsx', '.xls']:
                return pd.read_excel(self.file_path)
            else:
                raise ValueError(f"Unsupported file extension: {self.file_extension}. Supported formats: .csv, .xlsx, .xls")
        except Exception as e:
            print(f"Error reading file {self.file_path}: {str(e)}")
            raise
    
    def load_data(self, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Load evaluation data from CSV/XLSX file with proper error handling.
        
        Args:
            limit: Optional limit on the number of rows to load
            
        Returns:
            List of dictionaries containing the evaluation inputs
        """
        try:
            df = self._read_file()
            
            if limit:
                df = df.head(limit)
            
            print(f"Loading {len(df)} rows from {self.file_path}")
            
            evaluation_inputs = []
            for idx, row in df.iterrows():
                try:
                    # Handle metadata column if it exists
                    metadata = {"row_index": idx}
                    if 'metadata' in df.columns and pd.notna(row['metadata']):
                        if isinstance(row['metadata'], str):
                            try:
                                parsed_metadata = json.loads(row['metadata'])
                                metadata.update(parsed_metadata)
                            except json.JSONDecodeError:
                                print(f"Warning: Invalid JSON in metadata for row {idx}")
                                metadata['raw_metadata'] = str(row['metadata'])
                    
                    # Extract field values
                    prd_value = row.get('prd_document', '')
                    tech_spec_value = row.get('technical_document', '')
                    test_format_value = row.get('test_format', '')
                    test_plan_value = row.get('test_plan', '')
                    
                    # Create input structure with values at both top level and in 'input' object
                    eval_input = {
                        "input": {
                            "test_plan": test_plan_value,
                            "prd_document": prd_value,
                            "technical_document": tech_spec_value,
                            "test_format": test_format_value
                        },
                        "test_plan": test_plan_value,
                        "prd_document": prd_value,
                        "technical_document": tech_spec_value,
                        "test_format": test_format_value,
                        "output": test_plan_value,
                        "metadata": metadata
                    }
                    evaluation_inputs.append(eval_input)
                    
                except Exception as e:
                    print(f"Error processing row {idx}: {e}")
                    continue
            
            print(f"Successfully loaded {len(evaluation_inputs)} evaluation inputs")
            return evaluation_inputs
            
        except Exception as e:
            print(f"Error loading CSV data: {e}")
            # Return fallback data
            return [
                {
                    "input": {
                        "test_plan": "Sample test plan JSON",
                        "prd_document": "Sample PRD document",
                        "technical_document": "Sample technical document", 
                        "test_format": "Sample test format specification"
                    },
                    "metadata": {
                        "complexity": "medium",
                        "source": "fallback_data"
                    }
                }
            ]

def load_testplan_data() -> List[Dict[str, Any]]:
    """
    Load Test Plan evaluation dataset using the DataLoader.
    
    Returns:
        List of dictionaries containing the evaluation inputs
    """
    loader = DataLoader(DATA_PATH)
    return loader.load_data()

def testplan_agent_task(input_data: Union[Dict[str, Any], Any]) -> str:
    """
    Extract test plan from input data for evaluation.
    
    Args:
        input_data: The input data, either as a dictionary or another type
        
    Returns:
        The extracted test plan as a string
    """
    if isinstance(input_data, dict) and "input" in input_data:
        actual_input = input_data["input"]
    else:
        actual_input = input_data
    
    if isinstance(actual_input, dict):
        test_plan = actual_input.get("test_plan", "")
    else:
        test_plan = str(actual_input)
    
    return test_plan

#------------------------------------------------------------------------------
# TEST PLAN EVALUATION
#------------------------------------------------------------------------------
class TestPlanEvaluator:
    """
    Handles evaluation of test plans using the OpenAILLMClassifier.

    This class encapsulates the functionality for scoring test plans using
    the Braintrust OpenAILLMClassifier with custom system prompts.
    """
    
    def __init__(self, model_name: str = MODEL_NAME, temperature: float = TEMPERATURE):
        """
        Initialize the TestPlanEvaluator.
        
        Args:
            model_name: Name of the LLM model to use
            temperature: Temperature setting for the LLM (0-1)
        """
        self.model_name = model_name
        self.temperature = temperature
        self.prompt_path = PROMPT_PATH
        self._load_prompt()
    
    def _load_prompt(self) -> None:
        """Load the evaluation prompt from the prompt file."""
        try:
            self.prompt = load_prompt_from_file(self.prompt_path)
        except Exception as e:
            print(f"Error loading prompt: {e}")
            raise
    
    def _create_classifier(self) -> OpenAILLMClassifier:
        """
        Create an OpenAILLMClassifier with the appropriate configuration.

        Returns:
            Configured OpenAILLMClassifier instance
        """
        # Create messages with system prompt
        messages = [
            {"role": "system", "content": self.prompt},
            # User message will be filled in during evaluation
        ]

        # Build classification tools for chain of thought
        choice_strings = list(GRADE_SCORES.keys())
        classification_tools = build_classification_tools(useCoT=True, choice_strings=choice_strings)

        return OpenAILLMClassifier(
            name="TestPlan LLM Judge",
            messages=messages,
            model=self.model_name,
            choice_scores=GRADE_SCORES,
            classification_tools=classification_tools,
            max_tokens=MAX_TOKENS,
            temperature=self.temperature,
            client=braintrust_client
        )
    
    def _create_error_score(self, error_message: str) -> Score:
        """
        Create a Score object for error cases.
        
        Args:
            error_message: Description of the error
            
        Returns:
            Score object with error information
        """
        return Score(
            name="TestPlan LLM Judge",
            score=0.0,
            metadata={
                "error": error_message,
                "grade": "F"
            }
        )
    
    def evaluate(self, 
                test_plan: str, 
                prd_document: str = '', 
                technical_document: str = '', 
                test_format: str = '') -> Score:
        """
        Evaluate a test plan using the LLM classifier.
        
        Args:
            test_plan: The test plan to evaluate
            prd_document: The PRD document for context
            technical_document: The technical document for context
            test_format: The test format specification
            
        Returns:
            Score object containing the evaluation results
        """
        try:
            # Handle missing test plan data
            if not test_plan:
                print(f"Warning: Missing required test_plan data - skipping LLM classification")
                return self._create_error_score("Missing test_plan data")
                
            # Format user message with the test plan and other documents
            user_message = f"""
            Please evaluate the test plan.
            """
            
            # Create the classifier
            classifier = self._create_classifier()
            
            # Update the user message
            classifier.messages.append({"role": "user", "content": user_message})
                        
            # Run evaluation
            result = classifier(
                output="Test plan evaluation",
                expected="",
                test_plan=test_plan,
                prd_document=prd_document,
                technical_document=technical_document,
                test_format=test_format
            )
            
            return result
            
        except Exception as e:
            print(f"OpenAILLMClassifier error: {e}")
            return self._create_error_score(f"OpenAILLMClassifier failed: {str(e)}")

def testplan_scorer(output: Optional[str] = None, expected: Optional[str] = None, **kwargs) -> Score:
    """
    Score test plans using OpenAILLMClassifier from autoevals.
    This function extracts the test plan and related data from the input and
    passes it to the TestPlanEvaluator.
    
    Args:
        output: Output string that may contain the test plan
        expected: Expected output (not used in this context)
        **kwargs: Additional keyword arguments
        
    Returns:
        Score object containing the evaluation results
    """
    try:
        # Extract input data from various possible locations
        input_data = kwargs.get('input', {})
        
        # Try to find test_plan in various locations
        test_plan = input_data.get('test_plan', output or '')
        
        if not test_plan and isinstance(input_data, dict):
            if 'test_plan' in kwargs:
                test_plan = kwargs['test_plan']
            elif 'output' in kwargs:
                test_plan = kwargs['output']
                
        # Extract other required fields
        prd_document = input_data.get('prd_document', '')
        technical_document = input_data.get('technical_document', '')
        test_format = input_data.get('test_format', '')
        
        # Check kwargs for fields if not found in input_data
        if not prd_document and 'prd_document' in kwargs:
            prd_document = kwargs['prd_document']
        if not technical_document and 'technical_document' in kwargs:
            technical_document = kwargs['technical_document']
        if not test_format and 'test_format' in kwargs:
            test_format = kwargs['test_format']
        
        # Create evaluator and run evaluation
        evaluator = TestPlanEvaluator()
        return evaluator.evaluate(
            test_plan=test_plan,
            prd_document=prd_document,
            technical_document=technical_document,
            test_format=test_format
        )
        
    except Exception as e:
        print(f"Error in testplan_scorer: {e}")
        from autoevals import Score
        return Score(
            name="TestPlan LLM Judge",
            score=0.0,
            metadata={
                "error": str(e),
                "grade": "F"
            }
        )

#------------------------------------------------------------------------------
# EVALUATION DEFINITION
#------------------------------------------------------------------------------

# Define the evaluation for Braintrust
Eval(
    name=PROJECT_NAME,
    data=load_testplan_data,
    task=lambda input_data: testplan_agent_task(input_data),
    scores=[
        testplan_scorer
    ],
    metadata={
        "agent_type": "TestPlan",
        "version": "4.0.0",
        "description": f"Evaluates Test Plan agent using OpenAILLMClassifier with {MODEL_NAME} model"
    }
) 

#------------------------------------------------------------------------------
# API INTERACTION AND REPORTING
#------------------------------------------------------------------------------
class ExperimentFetcher:
    """
    Handles fetching experiment data from the Braintrust API.
    
    This class encapsulates the functionality for retrieving experiment
    details and events from the Braintrust API.
    """
    
    def __init__(self, project_name: str = PROJECT_NAME):
        """
        Initialize the ExperimentFetcher.
        
        Args:
            project_name: Name of the Braintrust project
        """
        self.project_name = project_name
        self.api_key = os.getenv("BRAINTRUST_API_KEY")
        if not self.api_key:
            raise ValueError("BRAINTRUST_API_KEY not found in environment variables")
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}"
        }
    
    def get_most_recent_experiment(self) -> tuple[str, str]:
        """
        Get the most recent experiment for the project.
        
        Returns:
            Tuple containing (experiment_id, experiment_name)
            
        Raises:
            ValueError: If no experiments are found
            Exception: For API errors
        """
        try:
            params = {"project_name": self.project_name, "limit": 1}
            response = requests.get(EXPERIMENT_LIST_URL, headers=self.headers, params=params)
            response.raise_for_status()
            
            experiments = response.json().get("objects", [])
            if not experiments:
                raise ValueError(f"No experiments found for project '{self.project_name}'")
            
            experiment_id = experiments[0]["id"]
            experiment_name = experiments[0]["name"]
            
            return experiment_id, experiment_name
            
        except Exception as e:
            print(f"Error getting experiment list: {e}")
            raise
    
    def fetch_experiment_events(self, experiment_id: str) -> List[Dict[str, Any]]:
        """
        Fetch all events for an experiment with pagination.
        
        Args:
            experiment_id: ID of the experiment to fetch events for
            
        Returns:
            List of event dictionaries
            
        Raises:
            Exception: For API errors
        """
        fetch_url = f"{BRAINTRUST_API_BASE}/experiment/{experiment_id}/fetch"
        all_events = []
        cursor = None
        
        try:
            # Handle pagination
            while True:
                fetch_params = {"limit": 100}
                if cursor:
                    fetch_params["cursor"] = cursor
                
                response = requests.post(fetch_url, headers=self.headers, json=fetch_params)
                response.raise_for_status()
                
                data = response.json()
                events = data.get("events", [])
                all_events.extend(events)
                
                cursor = data.get("cursor")
                if not cursor or not events:
                    break
            
            return all_events
            
        except Exception as e:
            print(f"Error fetching experiment events: {e}")
            raise
    
    def filter_relevant_events(self, events: List[Dict[str, Any]], max_cases: int = 9) -> List[Dict[str, Any]]:
        """
        Filter events to find the most relevant test cases.
        
        Args:
            events: List of event dictionaries
            max_cases: Maximum number of test cases to return
            
        Returns:
            List of filtered event dictionaries
        """
        # First, group events by their input data to identify unique test cases
        test_cases = {}
        
        # Sort events to prioritize those with scores and evaluation data
        events.sort(key=lambda e: 1 if e.get("scores") and "TestPlan LLM Judge" in e.get("scores", {}) else 0, reverse=True)
        
        # Extract the relevant test cases
        for event in events:
            # We're most interested in events with scores
            if event.get("scores") and "TestPlan LLM Judge" in event.get("scores", {}):
                # Try to identify the test case
                input_data = event.get("input", {})
                test_case_id = None
                
                # For test case identification, we'll use different approaches
                if isinstance(input_data, dict):
                    # First try to use row_index if available
                    if "row_index" in input_data:
                        test_case_id = f"row_{input_data['row_index']}"
                    # If no row_index, check if there's a unique hash in the span ID
                    elif event.get("span_id"):
                        test_case_id = f"span_{event['span_id'][:8]}"
                    # If still no ID, try using the root_span_id
                    elif event.get("root_span_id"):
                        test_case_id = f"root_{event['root_span_id'][:8]}"
                
                # If we found a test case ID and it's not already recorded, save this event
                if test_case_id and test_case_id not in test_cases:
                    test_cases[test_case_id] = event
        
        # If we don't have enough test cases, try another approach
        if len(test_cases) < max_cases:
            # Find events with rationales in metadata, which are likely evaluation results
            for event in events:
                metadata = event.get("metadata", {})
                if metadata and metadata.get("rationale") and metadata.get("choice"):
                    # This is definitely an evaluation result
                    span_id = event.get("span_id", "unknown")
                    test_case_id = f"eval_{span_id[:8]}"
                    
                    if test_case_id not in test_cases:
                        test_cases[test_case_id] = event
        
        # Take the first N test cases (or all if fewer)
        relevant_events = list(test_cases.values())
        if len(relevant_events) > max_cases:
            relevant_events = relevant_events[:max_cases]
            
        return relevant_events

class ReportGenerator:
    """
    Generates markdown reports from experiment data.
    
    This class handles the creation of readable markdown reports
    from the experiment events data.
    """
    
    def __init__(self):
        """Initialize the ReportGenerator."""
        pass
    
    def generate_report(self, 
                      experiment_id: str, 
                      events: List[Dict[str, Any]]) -> str:
        """
        Generate a markdown report from experiment events.
        
        Args:
            experiment_id: ID of the experiment
            events: List of event dictionaries to include in the report
            
        Returns:
            Filename of the generated report
        """
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"test_plan_evaluation_results_{timestamp}.md"
        
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"# Test Plan Evaluation Results\n\n")
            f.write(f"## Summary\n\n")
            f.write(f"- **Experiment ID**: {experiment_id}\n")
            f.write(f"- **Date**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- **Total Test Cases**: {len(events)}\n\n")
            
            # Process each relevant event
            for i, event in enumerate(events, 1):
                f.write(f"## Test Case {i}\n\n")
                
                # Extract row index using helper function
                row_index = extract_row_index(event)
                if row_index is not None:
                    f.write(f"**Row Index**: {row_index}\n\n")
                
                # Extract test plan using helper function
                test_plan = extract_test_plan(event)
                
                # Write test plan preview if found
                if test_plan:
                    # Use the helper function to extract the test suite name
                    test_suite_name, success = extract_test_suite_name(test_plan)
                    
                    if success:
                        # Write the extracted test suite name
                        f.write(f"**Test Suite Name**: {test_suite_name}\n\n")
                    else:
                        # If extraction failed, use the first 50 chars as fallback
                        test_plan_hint = str(test_plan)[:50] + "..."
                        f.write(f"**Test Plan Preview**: {test_plan_hint}\n\n")
                
                # Extract scores
                scores = event.get("scores", {})
                if scores:
                    f.write("### Evaluation Score\n\n")
                    for score_name, score_value in scores.items():
                        f.write(f"**{score_name}**: {score_value}\n\n")
                
                # Extract metadata (contains choice, rationale, etc.)
                metadata = event.get("metadata", {})
                if metadata:
                    f.write("### Evaluation Details\n\n")
                    
                    # Extract grade/choice
                    choice = metadata.get("choice", "N/A")
                    f.write(f"**Choice**: {choice}\n\n")
                    
                    # Extract rationale
                    rationale = metadata.get("rationale", "")
                    if rationale:
                        f.write("**Rationale**:\n\n")
                        f.write(f"```\n{rationale}\n```\n\n")
                
                f.write("---\n\n")
        
        print(f"Results saved to {filename}")
        return filename

def fetch_experiment_details_to_markdown(experiment_id: Optional[str] = None) -> Optional[str]:
    """
    Fetch experiment details from Braintrust API and save to a markdown file.
    
    Args:
        experiment_id: ID of the experiment to fetch (if not provided, will get most recent)
        
    Returns:
        Filename of the generated markdown report, or None if there was an error
        
    Raises:
        Exception: For API errors and other issues
    """
    try:
        # Create fetcher and get experiment ID if not provided
        fetcher = ExperimentFetcher()
        
        if not experiment_id:
            experiment_id, experiment_name = fetcher.get_most_recent_experiment()
            print(f"Using most recent experiment: {experiment_name} (ID: {experiment_id})")
        
        # Fetch and filter events
        all_events = fetcher.fetch_experiment_events(experiment_id)
        print(f"Fetched {len(all_events)} total events from experiment")
        
        relevant_events = fetcher.filter_relevant_events(all_events)
        print(f"Filtered down to {len(relevant_events)} relevant test case evaluation events")
        
        # Generate the report
        report_generator = ReportGenerator()
        filename = report_generator.generate_report(experiment_id, relevant_events)
        
        return filename
        
    except Exception as e:
        print(f"Error fetching experiment details: {e}")
        traceback.print_exc()
        return None

#------------------------------------------------------------------------------
# SCRIPT EXECUTION
#------------------------------------------------------------------------------

def main():
    """Main function to handle script execution."""
    parser = argparse.ArgumentParser(description="Test Plan Evaluation Script")
    parser.add_argument("--fetch", action="store_true", 
                        help="Fetch experiment details and save to markdown file")
    parser.add_argument("--experiment-id", type=str, 
                        help="Experiment ID to fetch (if not provided, will use most recent)")
    
    args = parser.parse_args()
    
    if args.fetch:
        print("Fetching experiment details...")
        fetch_experiment_details_to_markdown(args.experiment_id)
    else:
        print("Running in standard evaluation mode. Use --fetch to fetch experiment details.")
        print("Use --help for more information about available options.")

if __name__ == "__main__":
    import argparse
    main()